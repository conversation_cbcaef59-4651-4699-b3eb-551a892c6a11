<template>
  <web-view
    v-if="httpUrl"
    :src="httpUrl"
    @message="handleWebViewMessage"
  ></web-view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { getBaseByEnv } from "@/config";
import { onShow } from "@dcloudio/uni-app";

const httpUrl = ref("");

onShow(buildUrl);

function buildUrl() {
  const _env = uni.getStorageSync("MOBD_EVN_VERSION");

  let url = getBaseByEnv(_env === "release");

  const opt = uni.getEnterOptionsSync();

  const { path, ...other } = opt.query || {};

  if (path) {
    other._ts = Date.now()

    const query = Object.entries(other)
      .map(([key, value]) => `${key}=${value}`)
      .join("&");

    url += `#${path}?${query}`;
  }

  httpUrl.value = url;

  console.log('enter options', opt)
  console.log("build url:", httpUrl.value);
}

// 处理来自web-view的消息
function handleWebViewMessage(event: any) {
  const { data } = event.detail;

  if (data && data.length > 0) {
    const message = data[0];

    // 处理扫码请求
    if (message.type === 'scanCode') {
      handleScanCode(message);
    }
  }
}

// 扫码功能实现
function handleScanCode(message: any) {
  const { scanType = ['qrCode', 'barCode'], onlyFromCamera = false, callbackId } = message;

  uni.scanCode({
    scanType: scanType,
    onlyFromCamera: onlyFromCamera,
    success: (res) => {
      console.log('扫码成功:', res);

      // 将扫码结果发送回web-view
      const result = {
        type: 'scanCodeResult',
        callbackId: callbackId,
        success: true,
        data: {
          result: res.result,
          scanType: res.scanType,
          charSet: res.charSet,
          path: res.path
        }
      };

      // 通过evalJS向web-view发送消息
      postMessageToWebView(result);
    },
    fail: (err) => {
      console.log('扫码失败:', err);

      // 将错误信息发送回web-view
      const result = {
        type: 'scanCodeResult',
        callbackId: callbackId,
        success: false,
        error: {
          errMsg: err.errMsg,
          errCode: err.errCode || -1
        }
      };

      postMessageToWebView(result);
    }
  });
}

// 向web-view发送消息
function postMessageToWebView(data: any) {
  // 注意：这里需要根据实际的web-view实现来调整
  // 如果web-view支持evalJS，可以使用以下方式
  try {
    const webview = uni.createSelectorQuery().select('web-view').context();
    if (webview && webview.evalJS) {
      webview.evalJS(`
        if (window.receiveNativeMessage) {
          window.receiveNativeMessage(${JSON.stringify(data)});
        }
      `);
    }
  } catch (error) {
    console.error('向web-view发送消息失败:', error);
  }
}
</script>

<style></style>
